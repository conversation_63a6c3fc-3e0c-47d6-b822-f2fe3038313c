
<template>
	<view class="page">
		<u-picker :show="showCity" ref="uPicker" :loading="loading" :columns="columnsCity" @change="changeHandler"
			keyName="title" @cancel="showCity = false" @confirm="confirmCity" v-if="flag"></u-picker>
		<view class="top">个人信息隐私信息完全保密</view>
		<view class="main">
			<view class="main_item " @tap="goMap">
				<view class="name">服务地址</view>
				<view class="address">
					<span>{{form.address}}</span>
				</view>
				<image src="../static/images/position.png" mode=""></image>
				</view>
			<!-- 备用位置获取按钮，当主要方法失败时显示 -->
			<view class="fallback_location" @tap="getLocationFallback" v-if="form.address === '点击选择服务地址'">
				<text class="fallback_text">或点击获取当前位置</text>
			</view>
		<view class="main_item">
				<view class="name">门牌号</view>
				<input type="text" v-model="form.houseNumber" placeholder="请输入详细地址，如7栋4单元18a">
			</view>
			<view class="main_item">
				<view class="name">联系人</view>
				<input type="text" v-model="form.userName" placeholder="请输入姓名">
			</view>
			<view class="main_item">
				<view class="name">性别</view>
				<view class="box">
					<view class="box_item"
						:style="form.sex == 1?'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE':''"
						@tap="form.sex = 1">先生</view>
					<view class="box_item"
						:style="form.sex == 2?'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE':''"
						@tap="form.sex = 2">女士</view>
				</view>
			</view>
			<view class="main_item">
				<view class="name">手机号码</view>
				<input type="tel" v-model="form.mobile" placeholder="请输入手机号码">
			</view>
			<view class="main_item last">
				<view class="name">设为默认地址</view>
				<u-switch v-model="form.status" activeColor="#2E80FE"></u-switch>
			</view>
		</view>
		<view class="btn" @click="SaveAddress">保存</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				flag: false,
				loading: false,
				showCity: false,
				menpai: '',
				
				form: {
					userName: '',
					mobile: '',
					address: '点击选择服务地址',
					addressInfo: '',
					houseNumber: '',
					city: '',
					cityIds: '',
					lng: '',
					lat: '',
				
					sex: 1,
					status: false,
					provinceId: 0,
					cityId: 0,
					areaId: 0
				},
				columnsCity: [
					[], // Province
					[], // City
					[]  // Area
				],
			}
		},
		onLoad() {
			// this.getCity(0)
			this.getNowPosition()
		},
		methods: {
			getNowPosition() {
				return new Promise((resolve) => {
					uni.getLocation({
						type: "gcj02",
						isHighAccuracy: true,
						accuracy: "best",
						success: (res) => {
							uni.setStorageSync("lat", res.latitude);
							uni.setStorageSync("lng", res.longitude);
							uni.request({
								url: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,
								success: (res1) => {
									console.log(res1)
									this.form.address = res1.data.regeocode.formatted_address
									// Convert formatted_address to cityIds format similar to goMap()
									this.form.cityIds = res1.data.regeocode.formatted_address
									  ? res1.data.regeocode.formatted_address.replace(/(.+省)(.+市)(.+?县|.+?区).*/, '$1,$2,$3')
									  : '';
									this.form.city = res1.data.regeocode.formatted_address
									  ? res1.data.regeocode.formatted_address.replace(/(.+省)(.+市)(.+?县|.+?区).*/, '$1-$2-$3')
									  : '';
									// Store coordinates
									this.form.lng = res.longitude;
									this.form.lat = res.latitude;
									resolve();
								},
								fail: (err) => {
									console.error("逆地理编码失败:", err);
									resolve();
								}
							});
						},
						fail: (err) => {
							console.error("获取定位失败:", err);
							resolve();
						}
					});
				});
			},
			
			
			
			
			goMap() {
				let that = this
				// #ifdef MP-WEIXIN
				uni.authorize({
					scope: 'scope.userLocation',
					success(res) {
						// 添加延时和错误处理
						setTimeout(() => {
							uni.chooseLocation({
								success: function(res) {
									console.log('选择位置成功:', res);
									try {
										// Split address into province, city, county format, discarding anything after county
										// Use 'that' to correctly reference 'this.form.cityIds'
										that.form.cityIds = res.address
										  ? res.address.replace(/(.+省)(.+市)(.+?县|.+?区).*/, '$1,$2,$3')
										  : '';
										that.form.city = res.address
										  ? res.address.replace(/(.+省)(.+市)(.+?县|.+?区).*/, '$1-$2-$3')
										  : '';
										console.log('处理后的cityIds:', that.form.cityIds);
										console.log('处理后的city:', that.form.city);

										that.form.address = res.name || '未知位置'
										that.form.addressInfo = res.address || ''
										that.form.lng = res.longitude || ''
										that.form.lat = res.latitude || ''

										uni.showToast({
											title: '位置选择成功',
											icon: 'success',
											duration: 1500
										});
									} catch (error) {
										console.error('处理位置信息时出错:', error);
										uni.showToast({
											title: '位置信息处理失败',
											icon: 'none',
											duration: 2000
										});
									}
								},
								fail: function(err) {
									console.error('选择位置失败:', err);
									uni.showToast({
										title: '选择位置失败，请重试',
										icon: 'none',
										duration: 2000
									});
								}
							});
						}, 300); // 延时300ms避免框架内部状态问题
					},
					fail(err) {
						console.error('位置授权失败:', err)
						uni.showToast({
							title: '请授权位置信息',
							icon: 'none',
							duration: 2000
						});
					}
				})
				// #endif
				// #ifdef APP
				// APP端需要更长的延时来避免内部状态问题
				setTimeout(() => {
					try {
						uni.chooseLocation({
							success: function(res) {
								console.log('APP选择位置成功:', res)
								try {
									// APP端也需要处理cityIds和city字段，与小程序端保持一致
									if (res.address) {
										that.form.cityIds = res.address.replace(/(.+省)(.+市)(.+?县|.+?区).*/, '$1,$2,$3');
										that.form.city = res.address.replace(/(.+省)(.+市)(.+?县|.+?区).*/, '$1-$2-$3');
									} else {
										that.form.cityIds = '';
										that.form.city = '';
									}

									that.form.address = res.name || '未知位置'
									that.form.addressInfo = res.address || ''
									that.form.lng = res.longitude || ''
									that.form.lat = res.latitude || ''

									console.log('APP端处理后的cityIds:', that.form.cityIds);
									console.log('APP端处理后的city:', that.form.city);

									uni.showToast({
										title: '位置选择成功',
										icon: 'success',
										duration: 1500
									});
								} catch (error) {
									console.error('APP处理位置信息时出错:', error);
									// 即使处理出错，也要设置基本信息
									that.form.address = res.name || '位置信息'
									that.form.addressInfo = res.address || ''
									that.form.lng = res.longitude || ''
									that.form.lat = res.latitude || ''

									uni.showToast({
										title: '位置信息处理部分失败',
										icon: 'none',
										duration: 2000
									});
								}
							},
							fail: function(err) {
								console.error('APP选择位置失败:', err);
								uni.showToast({
									title: '选择位置失败，请检查权限设置',
									icon: 'none',
									duration: 2500
								});
							}
						});
					} catch (globalError) {
						console.error('APP端chooseLocation调用失败:', globalError);
						uni.showToast({
							title: '地图功能暂时不可用',
							icon: 'none',
							duration: 2000
						});
					}
				}, 500); // APP端使用更长的延时
				// #endif
			},

			// 备用的位置获取方法
			async getLocationFallback() {
				try {
					const res = await uni.getLocation({
						type: 'gcj02',
						isHighAccuracy: true,
						accuracy: 'best'
					});

					// 使用高德地图API进行逆地理编码
					const geoRes = await uni.request({
						url: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,
						method: 'GET'
					});

					if (geoRes.data && geoRes.data.regeocode) {
						this.form.address = geoRes.data.regeocode.formatted_address || '当前位置';
						this.form.addressInfo = geoRes.data.regeocode.formatted_address || '';
						this.form.lng = res.longitude;
						this.form.lat = res.latitude;

						// 处理城市信息
						if (geoRes.data.regeocode.formatted_address) {
							this.form.cityIds = geoRes.data.regeocode.formatted_address.replace(/(.+省)(.+市)(.+?县|.+?区).*/, '$1,$2,$3');
							this.form.city = geoRes.data.regeocode.formatted_address.replace(/(.+省)(.+市)(.+?县|.+?区).*/, '$1-$2-$3');
						}

						uni.showToast({
							title: '已获取当前位置',
							icon: 'success',
							duration: 1500
						});
					}
				} catch (error) {
					console.error('备用位置获取失败:', error);
					uni.showToast({
						title: '位置获取失败，请手动输入',
						icon: 'none',
						duration: 2000
					});
				}
			},
			confirmCity(Array) {
				// Map selected values to titles and IDs
				const selectedValues = Array.value
				const titles = selectedValues.map((item, index) => {
					return item?.title || this.columnsCity[index][0]?.title || ''
				})
				const ids = selectedValues.map((item, index) => {
					return item?.id || this.columnsCity[index][0]?.id || 0
				}).filter(id => id !== null && id !== undefined)

				this.form.city = titles.join('-')
				// Set cityIds as nested array [[provinceId, cityId, areaId]]
				this.form.cityIds = ids.length >= 3 ? [[ids[0], ids[1], ids[2]]] : [[0, 0, 0]]
				// Set individual IDs
				this.form.provinceId = ids[0] || 0
				this.form.cityId = ids[1] || 0
				this.form.areaId = ids[2] || 0
				this.showCity = false
			},
			getCity(e) {
				this.$api.service.getCity(e).then(res => {
					this.columnsCity[0] = res
					if (res[0]?.id) {
						this.$api.service.getCity(res[0].id).then(res1 => {
							this.columnsCity[1] = res1
							if (res1[0]?.id) {
								this.$api.service.getCity(res1[0].id).then(res2 => {
									this.columnsCity[2] = res2
									this.flag = true
									
								})
							}
						})
					}
				}).catch(err => {
					console.error('Failed to fetch city data:', err)
				})
			},
			changeHandler(e) {
				const {
					columnIndex,
					index,
					picker = this.$refs.uPicker
				} = e
				if (columnIndex === 0) {
					if (this.columnsCity[0][index]?.id) {
						this.$api.service.getCity(this.columnsCity[0][index].id).then(res => {
							picker.setColumnValues(1, res)
							this.columnsCity[1] = res
							if (res[0]?.id) {
								this.$api.service.getCity(res[0].id).then(res1 => {
									picker.setColumnValues(2, res1)
									this.columnsCity[2] = res1
										console.log(res1)
								})
							}
						})
					}
				} else if (columnIndex === 1) {
					if (this.columnsCity[1][index]?.id) {
						this.$api.service.getCity(this.columnsCity[1][index].id).then(res => {
							picker.setColumnValues(2, res)
							this.columnsCity[2] = res
							console.log(res)
						})
					}
				}
			},
			async SaveAddress() {
				if (this.form.address === '点击选择服务地址') {
					uni.showToast({
						icon: 'none',
						title: '请填写完整提交',
						findduration: 1500
					})
					return
				}
				let phoneReg = /^1[3456789]\d{9}$/
				if (!phoneReg.test(this.form.mobile)) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的手机号',
						duration: 1500
					})
					return
				}
				console.log(this.form)
				for (let key of ['userName', 'mobile', 'address', 'houseNumber']) {
					if (this.form[key] === '') {
						uni.showToast({
							icon: 'none',
							title: '请填写完整提交',
							duration: 1500
						})
						return
					}
				}
				if (!this.form.cityIds.length || this.form.cityIds[0].includes(0)) {
					uni.showToast({
						icon: 'none',
						title: '请选择所在区域',
						duration: 1500
					})
					return
				}
				if (this.form.cityIds) { // Only call if cityIds has a value from goMap
				  try {
				    const res = await this.$api.service.getZhuanhuan({
				      mergeName: this.form.cityIds
				    });
				    console.log(res);
				    if (res.data) {
				      // Construct the comma-separated string from the individual IDs
				      this.form.cityIds = `${res.data.provinceId},${res.data.cityId},${res.data.areaId}`;
				console.log(this.form.cityIds)
				    } else {
				      this.form.cityIds = ''; // Handle cases where res.data might be null or undefined
				    }
				  } catch (err) {
				    console.error("Error converting cityIds:", err);
				    uni.showToast({
				      icon: 'none',
				      title: '城市信息转换失败',
				      duration: 1500,
				    });
				    return; // Stop execution if conversion fails
				  }
				}
				// Prepare form data for API
				let userId= uni.getStorageSync('userInfo')
				console.log(userId)
				console.log(this.form)
				// console.log(JSON.parse(userId))
				let subForm = {
					address: this.form.address,
					addressInfo: this.form.addressInfo,
					// areaId: this.form.areaId,
					city: this.form.city,
					// cityId: this.form.cityId,
					cityIds: this.form.cityIds, // [[provinceId, cityId, areaId]]
					// createTime: 0,
					houseNumber: this.form.houseNumber,
					// id: 0,
					lat: this.form.lat,
					lng: this.form.lng,
					mobile: this.form.mobile,
					// provinceId: this.form.provinceId,
					sex: this.form.sex,
					status: this.form.status ? 1 : 0,
					// top: 0,
					// uniacid: this.form.uniacid,
					
					// userId:userId.userId,
					userName: this.form.userName
				}
				this.$api.service.postAddAddress(subForm).then(res => {
					if(res.code==='200'){
						uni.showToast({
							icon: 'none',
							title: '提交成功',
							duration: 1000
						})
						setTimeout(() => {
							uni.navigateBack({ delta: 1 })
						}, 1000)
					}else{
						uni.showToast({
							icon: 'none',
							title: err.msg || '提交失败，重新尝试',
							duration: 1500
						})
					}
				}).catch(err => {
					uni.showToast({
						icon: 'none',
						title: err.msg || '提交失败',
						duration: 1500
					})
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		height: 100vh;
		background-color: #fff;

		.top {
			width: 750rpx;
			height: 58rpx;
			background: #FFF7F1;
			font-size: 28rpx;
			font-weight: 400;
			color: #FE921B;
			line-height: 58rpx;
			text-align: center;
		}

		.btn {
			margin: 0 auto;
			margin-top: 88rpx;
			width: 690rpx;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 50rpx 50rpx 50rpx 50rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 98rpx;
			text-align: center;
		}

		.main {
			padding: 0 30rpx;

			.main_item {
				padding: 40rpx 0;
				border-bottom: 2rpx solid #E9E9E9;
				display: flex;
				align-items: center;
				position: relative;

				.name {
					min-width: 112rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: #333333;
					margin-right: 40rpx;
				}

				.address {
					font-size: 28rpx;
					font-weight: 400;
					color: #ADADAD;

					.details {
						margin-top: 20rpx;
						font-size: 20rpx;
						font-weight: 400;
						color: #ADADAD;
					}
				}

				image {
					width: 23rpx;
					height: 27rpx;
					position: absolute;
					right: 0;
					top: 46rpx;
				}

				input {
					width: 450rpx;
				}

				.box {
					display: flex;
					align-items: center;

					.box_item {
						margin-right: 20rpx;
						width: 88rpx;
						height: 50rpx;
						background: #FFFFFF;
						border-radius: 4rpx;
						border: 2rpx solid #EDEDED;
						font-size: 28rpx;
						font-weight: 400;
						color: #ADADAD;
						line-height: 46rpx;
						text-align: center;
					}
				}
			}

			.last {
				justify-content: space-between;

				.name {
					width: 170rpx;
				}
			}
		}

		.fallback_location {
			padding: 20rpx 30rpx;
			text-align: center;

			.fallback_text {
				font-size: 24rpx;
				color: #2E80FE;
				text-decoration: underline;
			}
		}
	}
</style>
```